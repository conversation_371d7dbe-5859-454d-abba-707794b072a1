package com.kaolafm.kradio.user;

import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.kradio.common.http.CommonRequestParamsUtil;
import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.Logger;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.api.login.model.QRCodeInfo;
import com.kaolafm.opensdk.api.login.model.UserInfo;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.opensdk.socket.SocketApiConstants;
import com.kaolafm.opensdk.socket.SocketEvent;
import com.kaolafm.opensdk.socket.SocketListener;
import com.kaolafm.opensdk.socket.SocketManager;

import java.util.ArrayList;
import java.util.Map;

/**
 * <AUTHOR> on 2018/5/2.
 * 用户信息管理类
 */
public class UserInfoManager {

    private static final String TAG = "k.login.uim";

    private KaolaAccessToken mKaolaAccessToken;

    private ArrayList<IUserInfoStateListener> mUserInfoStateListenerArrayList;
    private UserInfoDataMemory mUserInfoDataMemory;

    private SocketListener<String> mLoginSocketListener;
    private SocketListener<UserLogoutInfo> mLogoutSocketListener;

    private boolean isUserLogin;

    private boolean isUserVip;

    private boolean loginStateChange;

    private boolean vipStateChange;

    private String lastUserId = "";

    private Integer qrCodeStatus = -1; //用户登录时候的二维码的状态

    private UserInfoManager() {
        mUserInfoStateListenerArrayList = new ArrayList<>();
        mUserInfoDataMemory = new UserInfoDataMemory();
        mKaolaAccessToken = AccessTokenManager.getInstance().getKaolaAccessToken();
        isUserLogin = mKaolaAccessToken.isLogin();
        if (isUserLogin) {
            lastUserId = getUserId();
        }
        isUserVip = mUserInfoDataMemory.getVip() == 1;
        AccessTokenManager.getInstance().registerObserver(kaolaAccessToken -> {
            mKaolaAccessToken = kaolaAccessToken == null ? new KaolaAccessToken() : kaolaAccessToken;
            if (!mKaolaAccessToken.isLogin()) {
                logout();
            }
        });
        Log.i(TAG, "初始化 登录和登出消息");

        mLoginSocketListener = new SocketListener<String>() {
            @Override
            public String getEvent() {
                return SocketEvent.USER_LOGIN;
            }

            @Override
            public Map<String, Object> getParams(Map<String, Object> params) {
                return params;
            }

            @Override
            public boolean isNeedParams() {
                return true;
            }

            @Override
            public boolean isNeedRequest() {
                return true;
            }

            @Override
            public void onSuccess(String s) {
                Log.i(TAG, "收到登录消息 success");

            }

            @Override
            public void onError(ApiException e) {
                Log.i(TAG, "收到登录消息 error");

            }
        };
        mLogoutSocketListener = new SocketListener<UserLogoutInfo>() {
            @Override
            public String getEvent() {
                return SocketEvent.USER_LOGOUT;
            }

            @Override
            public Map<String, Object> getParams(Map<String, Object> params) {
                return params;
            }

            @Override
            public boolean isNeedParams() {
                return false;
            }

            @Override
            public boolean isNeedRequest() {
                return false;
            }

            @Override
            public void onSuccess(UserLogoutInfo userLogoutInfo) {
                Log.i(TAG, "收到退出登录消息 success");
                kickOut(userLogoutInfo);
            }

            @Override
            public void onError(ApiException e) {
                Log.i(TAG, "收到退出登录消息 error");
                kickOut();
            }
        };
        SocketManager.getInstance().setMap(CommonRequestParamsUtil.getCommonParams()).setSocketHost(SocketApiConstants.SOCKET_HOST).request(mLogoutSocketListener);

        mUserInfoStateListenerArrayList.add(LoginManager.getInstance());
    }


    private static class USER_INFO_MANAGER {
        static final UserInfoManager USER_INFO_MANAGER_INSTANCE = new UserInfoManager();
    }

    public static UserInfoManager getInstance() {
        return USER_INFO_MANAGER.USER_INFO_MANAGER_INSTANCE;
    }

    /**
     * 用户是否已经登录.
     *
     * @return
     */
    public boolean isUserLogin() {
        return mKaolaAccessToken.isLogin();
    }

    /**
     * 获取openid
     *
     * @return
     */
    public String getOpenId() {
        return mKaolaAccessToken.getOpenId();
    }

    /**
     * 用户是否已经绑定
     *
     * @return
     */
    public boolean isUserBound() {
        //   Logging.d("是否登录，isUserLogin=%s, isLocalLogin=%s", isUserLogin(),mUserInfoDataMemory.isLocalLogin());
        return isUserLogin();//&& mUserInfoDataMemory.isLocalLogin();
    }

    /**
     * 设置用户激活结果 BY kaola服务器
     */
    public void setUserActivationByKaolaResult(String openid) {
        mUserInfoDataMemory.setOpenid(openid);
    }


    /**
     * 通知用户已经登录
     */
    private void notifyUserLogin() {
        Logger.i(TAG, "notifyUserLogin: mUserInfoStateListenerArrayList = " + mUserInfoStateListenerArrayList);
        if (mUserInfoStateListenerArrayList == null) {
            return;
        }
        ArrayList<IUserInfoStateListener> tempArrayList = (ArrayList<IUserInfoStateListener>) mUserInfoStateListenerArrayList.clone();
        int arrayLen = tempArrayList.size();
        for (int i = 0; i < arrayLen; i++) {
            IUserInfoStateListener iUserInfoStateListener = tempArrayList.get(i);
            if (iUserInfoStateListener != null) {
                iUserInfoStateListener.userLogin();
            }
        }
    }

    /**
     * 通知用户已经登出
     */
    private void notifyUserLogout() {
        if (mUserInfoStateListenerArrayList == null) {
            return;
        }
        ArrayList<IUserInfoStateListener> tempArrayList = (ArrayList<IUserInfoStateListener>) mUserInfoStateListenerArrayList.clone();
        int arrayLen = tempArrayList.size();
        for (int i = 0; i < arrayLen; i++) {
            IUserInfoStateListener iUserInfoStateListener = tempArrayList.get(i);
            if (iUserInfoStateListener != null) {
                iUserInfoStateListener.userLogout();
            }
        }
    }

    /**
     * 通知用户进入登录或者注册但是没有登录就退出了
     */
    public void notifyUserCancel() {
        if (mUserInfoStateListenerArrayList == null) {
            return;
        }
        ArrayList<IUserInfoStateListener> tempArrayList = (ArrayList<IUserInfoStateListener>) mUserInfoStateListenerArrayList.clone();
        int arrayLen = tempArrayList.size();
        for (int i = 0; i < arrayLen; i++) {
            IUserInfoStateListener iUserInfoStateListener = tempArrayList.get(i);
            if (iUserInfoStateListener != null) {
                iUserInfoStateListener.userCancel();
            }
        }
    }

    /**
     * 用户退出登录
     */
    public void logout() {
        Logging.d("用户退出登录");
        setUserFavicon(null);
        setUserNickName(null);
        setLivingUidToken(Constants.BLANK_STR);
        localLogout();
    }

    /**
     * 踢出
     */
    public void kickOut() {
        AccessTokenManager.getInstance().logoutAll();
    }

    public void kickOut(UserLogoutInfo userLogoutInfo) {
        kickOut();
        if (userLogoutInfo != null && !TextUtils.isEmpty(userLogoutInfo.msg)) {
            ToastUtil.showOnly(AppDelegate.getInstance().getContext(), userLogoutInfo.msg);
        }
    }

    /**
     * 设置 userid
     *
     * @param userId
     */
    public void setUserId(String userId) {
        mUserInfoDataMemory.setUserId(userId);
    }

    /**
     * 用户登录成功。设置本地登录状态
     */
    public void localLogin() {
        Logger.i(TAG, "localLogin: ");
        mUserInfoDataMemory.setLocalLogin(true);
        notifyUserLogin();
        SocketManager.getInstance().setMap(CommonRequestParamsUtil.getCommonParams()).setSocketHost(SocketApiConstants.SOCKET_HOST).request(mLoginSocketListener);
    }

    /**
     * 用户取消绑定。调用该接口是假退出，即服务器不解除绑定，客户端显示退出状态。
     */
    public void localLogout() {
        mUserInfoDataMemory.setLocalLogin(false);
        notifyUserLogout();
    }


    public String getUserId() {
        return mKaolaAccessToken.getUserId();
    }

    /**
     * 设置绑定后 用户昵称
     *
     * @return
     */
    public String getUserNickName() {
        return mUserInfoDataMemory.getPhoneAccountNickName();
    }

    /**
     * 获取绑定 用户昵称
     */
    public void setUserNickName(String userNickName) {
        mUserInfoDataMemory.setPhoneAccountNickName(userNickName);
    }

    /**
     * 获取绑定  用户的头像
     *
     * @return
     */
    public String getUserFavicon() {
        return mUserInfoDataMemory.getPhoneAccountFavicon();
    }

    /**
     * 设置绑定 用户头像
     *
     * @param userFavicon
     */
    public void setUserFavicon(String userFavicon) {
        mUserInfoDataMemory.setPhoneAccountFavicon(userFavicon);
    }

    public String getToken() {
        return mKaolaAccessToken.getAccessToken();
    }

    /**
     * 设置直播uid和token
     *
     * @param uidToken
     */
    public void setLivingUidToken(String uidToken) {
        mUserInfoDataMemory.setLivingUidToken(uidToken);
    }

    /**
     * 得到直播 uid和token
     *
     * @return
     */
    public String getLivingUidToken() {
        return mUserInfoDataMemory.getLivingUidToken();
    }

    /**
     * 获取是否是vip，vip类型
     *
     * @return
     */
    public int getVip() {
        return mUserInfoDataMemory.getVip();
    }

    public void setVip(int vip) {
        mUserInfoDataMemory.setVip(vip);
    }

    /**
     * 获取vip过期时间
     *
     * @return
     */
    public String getVipExpireDate() {
        return mUserInfoDataMemory.getVipExpireDate();
    }

    public void setVipExpireDate(String vipExpireDate) {
        mUserInfoDataMemory.setVipExpireDate(vipExpireDate);
    }

    /**
     * 获取vip剩余天数
     *
     * @return
     */
    public int getVipRemainDays() {
        return mUserInfoDataMemory.getVipRemainDays();
    }

    public void setVipRemainDays(int vipRemainDays) {
        mUserInfoDataMemory.setVipRemainDays(vipRemainDays);
    }

    public int getOldUser() {
        return mUserInfoDataMemory.getOldUser();
    }

    public void setOldUser(int oldUser) {
        mUserInfoDataMemory.setOldUser(oldUser);
    }

    public String getTips() {
        return mUserInfoDataMemory.getTips();
    }

    public void setTips(String tips) {
        mUserInfoDataMemory.setTips(tips);
    }

    public void setGender(String gender) {
        mUserInfoDataMemory.setGender(gender);
    }

    public String getGender() {
        return mUserInfoDataMemory.getGender();
    }

    public void setUserArea(String userArea) {
        mUserInfoDataMemory.setUserArea(userArea);
    }

    public String getUserArea() {
        return mUserInfoDataMemory.getUserArea();
    }

    public UserInfo getUserInfo() {
        return mUserInfoDataMemory.getUserInfo();
    }

    public String getLoginType() {
        return mUserInfoDataMemory.getLoginType();
    }

    public void setLoginType(String loginType) {
        mUserInfoDataMemory.setLoginType(loginType);
    }

    /**
     * 添加用户状态监听器
     *
     * @param iUserInfoStateListener
     */
    public void addUserInfoStateListener(IUserInfoStateListener iUserInfoStateListener) {
        Log.i(TAG, "addUserInfoStateListener iUserInfoStateListener = " + iUserInfoStateListener + " list is null = " + (mUserInfoStateListenerArrayList == null));
        if (iUserInfoStateListener == null) {
            return;
        }
        if (mUserInfoStateListenerArrayList == null) {
            return;
        }
        if (mUserInfoStateListenerArrayList.contains(iUserInfoStateListener)) {
            return;
        }
        mUserInfoStateListenerArrayList.add(iUserInfoStateListener);
    }

    /**
     * 删除用户状态监听器
     *
     * @param iUserInfoStateListener
     */
    public void removeUserInfoStateListener(IUserInfoStateListener iUserInfoStateListener) {
        if (iUserInfoStateListener == null || mUserInfoStateListenerArrayList == null) {
            return;
        }
        if (mUserInfoStateListenerArrayList.contains(iUserInfoStateListener)) {
            mUserInfoStateListenerArrayList.remove(iUserInfoStateListener);
        }
    }

    /**
     * 用户状态监听状态
     */
    public interface IUserInfoStateListener {
        /**
         * 登录
         */
        void userLogin();

        /**
         * 用户注销登录
         */
        void userLogout();

        /**
         * 用户进入登录或者注册但是没有登录就退出了
         */
        void userCancel();
    }

    public boolean isLoginStateChange() {
        boolean curState = isUserLogin();
        loginStateChange = isUserLogin != curState;
        isUserLogin = curState;
        if (loginStateChange) {
            if(curState) lastUserId = getUserId();
            return true;
        }
        if (isUserLogin()) {
            String curUid = getUserId();
            if (TextUtils.equals(lastUserId, curUid)) {
                return false;
            } else {
                lastUserId = curUid;
                return true;
            }
        }
        return loginStateChange;
    }

    public boolean isVipStateChange() {
        boolean curState = getVip() == 1;
        vipStateChange = isUserVip != curState;
        isUserVip = curState;
        return vipStateChange;
    }

    public Integer getQrCodeStatus() {
        return qrCodeStatus;
    }

    public void setQrCodeStatus(Integer qrCodeStatus) {
        this.qrCodeStatus = qrCodeStatus;
    }

    public boolean isQrScanned() {
        return qrCodeStatus == QRCodeInfo.STATUS_SCANED;
    }

}