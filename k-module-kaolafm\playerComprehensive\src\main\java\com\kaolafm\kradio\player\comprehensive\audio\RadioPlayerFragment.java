package com.kaolafm.kradio.player.comprehensive.audio;

import android.app.Activity;
import androidx.lifecycle.Lifecycle;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.constraintlayout.widget.Group;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.view.ViewTreeObserver;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.kaolafm.ad.expose.AdvertisingImager;
import com.kaolafm.ad.expose.AdvertisingManager;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.kradio.common.ViewConstants;
import com.kaolafm.kradio.lib.report.ReportUtil;
import com.kaolafm.kradio.common.router.RouterConstance;
import com.kaolafm.kradio.common.utils.BitmapUtils;
import com.kaolafm.kradio.common.utils.ThreadUtil;
import com.kaolafm.kradio.common.widget.RadioPlayerImageAnimLayout;
import com.kaolafm.kradio.component.ComponentClient;
import com.kaolafm.kradio.component.DynamicComponent;
import com.kaolafm.kradio.component.MainThreadable;
import com.kaolafm.kradio.component.RealCaller;
import com.kaolafm.kradio.constant.LiveComponentConst;
import com.kaolafm.kradio.constant.UserComponentConst;
import com.kaolafm.kradio.constant.UserStateObserverProcessorConst;
import com.kaolafm.kradio.k_kaolafm.R;
import com.kaolafm.kradio.lib.base.flavor.KRadioC211ViewSizeInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioMultiWindowInter;
import com.kaolafm.kradio.lib.base.flavor.KRadioPlayPageModifyInter;
import com.kaolafm.kradio.lib.base.ui.BaseActivity;
import com.kaolafm.kradio.lib.base.ui.BaseFragment;
import com.kaolafm.kradio.lib.toast.ToastUtil;
import com.kaolafm.kradio.lib.utils.AntiShake;
import com.kaolafm.kradio.lib.utils.ClazzImplUtil;
import com.kaolafm.kradio.lib.utils.ComponentUtil;
import com.kaolafm.kradio.lib.utils.Constants;
import com.kaolafm.kradio.lib.utils.NetworkUtil;
import com.kaolafm.kradio.lib.utils.ResUtil;
import com.kaolafm.kradio.lib.utils.ViewUtil;
import com.kaolafm.kradio.lib.utils.imageloader.ImageLoader;
import com.kaolafm.kradio.player.comprehensive.drag.DragController;
import com.kaolafm.kradio.player.comprehensive.drag.DragData;
import com.kaolafm.kradio.player.comprehensive.drag.DragLayer;
import com.kaolafm.kradio.player.comprehensive.PlayerFragmentHelper;
import com.kaolafm.kradio.player.comprehensive.SwitchPlayerHelper;
import com.kaolafm.kradio.player.comprehensive.play.view.AIRadioPlusFeedbackView;
import com.kaolafm.kradio.player.comprehensive.play.widget.RadioPlayListContent;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.kradio.player.helper.intercept.HintInterceptManager;
import com.kaolafm.kradio.player.helper.intercept.HintVoiceChainIntercept;
import com.kaolafm.kradio.player.radiolive.LiveLifecycleListener;
import com.kaolafm.kradio.player.radiolive.LiveStateManager;
import com.kaolafm.kradio.player.radiolive.RadioLiveInfo;
import com.kaolafm.kradio.player.utils.AudioSubscribeCacheUtil;
import com.kaolafm.kradio.player.utils.LiveInfoHelper;
import com.kaolafm.kradio.purchase.PayManager;
import com.kaolafm.kradio.purchase.constant.PayConst;
import com.kaolafm.kradio.purchase.model.PayResult;
import com.kaolafm.kradio.purchase.observer.AlbumPayListener;
import com.kaolafm.kradio.purchase.observer.AudiosPayListener;
import com.kaolafm.kradio.purchase.observer.VipPayListener;
import com.kaolafm.kradio.subscribe.SubscribeHelper;
import com.kaolafm.kradio.user.LoginManager;
import com.kaolafm.kradio.user.UserInfoManager;
import com.kaolafm.opensdk.api.media.model.AlbumDetails;
import com.kaolafm.opensdk.api.media.model.VideoAudioDetails;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.db.manager.RadioSortTypeDaoManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListControl;
import com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener;
import com.kaolafm.opensdk.player.logic.model.item.AlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.FeaturePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.LiveStreamPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.VideoAlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.AlbumPlayListControl;
import com.kaolafm.opensdk.player.logic.playlist.FeaturePlayListControl;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.report.ReportHelper;
import com.kaolafm.report.event.ButtonClickReportEvent;
import com.kaolafm.report.event.ButtonExposureOrClickReportEvent;
import com.kaolafm.report.event.LivingStartListenReportEvent;
import com.kaolafm.report.event.LoginReportEvent;
import com.kaolafm.report.event.PlayerUiControlReportEvent;
import com.kaolafm.report.util.ReportConstants;
import com.kaolafm.report.util.ReportParameterManager;

import java.util.List;
import kotlin.Unit;
import kotlin.jvm.functions.Function1;
import me.yokeyword.fragmentation.anim.DefaultNoAnimator;
import me.yokeyword.fragmentation.anim.FragmentAnimator;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;

/**
 * 音频详情列表页面
 * <p/>
 * 电台/专辑播放器，电台播放器在播放列表中的最后一首时，点击下一首按钮，加载下一页。专辑播放器会有下拉刷新
 * 和上拉加载更多。
 * <p/>
 * 专辑会有期数子标题。 如果是正常的播放，直接显示标题，如果是场景推荐，要等到播单更新后显示。
 *
 * <AUTHOR> Huangui
 */
@Route(path = RouterConstance.PLAY_RADIO_COMPREHENSIVE_URL)
public class RadioPlayerFragment extends BaseFragment<RadioPlayerPresenter> implements RadioPlayerView {

    private static final String TAG = "RadioPlayerFragment";

    public View mBackView;
    public View mPlayerTitleLine;
    ImageView mProgramImage;
    ImageView vip_icon;
    TextView mPlayerTitleText;
    public TextView mTotalNumberText;
    Group sortTypeGroup;
    TextView sortTipTv;
    ImageView sortIcon;
    ViewGroup sortTypeLayout;
    public RadioPlayListContent mPlayListView;
    int mReflectionStartColor;
    int mReflectionEndColor;
    RadioPlayerImageAnimLayout mImageAnimLayout;
    RadioLiveItemView mClLive;
    TextView mTvLivetitle;
    TextView tvBuy;
    TextView mTvLiveFlag;
    AIRadioPlusFeedbackView mAiRadioPlusFeedbackView;
    ConstraintLayout mRootLayout;
    RadioPlaySourceFoundView mRadioPlaySourceFoundView;
    ImageView mTopIcon;
    ViewStub playerBarViewStub;

    private static final float REFLECT_VALUE = 0.130F;

    private PlayItem mPlayItem;

    private PlayerFragmentHelper mPlayerFragmentHelper;

    private long mRadioId;
    private int mRadioType;

    private int mPrePlayPosition;
    private int mCurrPlayPosition;

    private boolean bIsSubscribed;

    private DynamicComponent mRadioPlayerUserObserver;
    private BasePlayStateListener iPlayerStateListener;
    private KRadioMultiWindowInter mKRadioMultiWindowInter;
    private DragController mDragController;
    private DragLayer mDragLayer;
    private View.OnLongClickListener mLongClickListener;
    private boolean hasPlayerBar;//是否显示播放器
    private BroadcastReceiver receiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            boolean isDiffRadio = mPlayerFragmentHelper.checkIsDiffRadio(mRadioId);
            if (!isDiffRadio) {
                if (mRadioType == PlayerConstants.RESOURCES_TYPE_ALBUM) {
                    getPayInfo();
                }
            }
        }
    };

//    public static RadioPlayerFragment getInstance() {
//        return new RadioPlayerFragment();
//    }

    private IPlayListStateListener mPlayListStateListener = new IPlayListStateListener() {
        @Override
        public void onPlayListChange(List<PlayItem> list) {
            SwitchPlayerHelper.getInstance().changePlayer(RadioPlayerFragment.this, PlayerManagerHelper.getInstance().getCurrentPlayType());
        }

        @Override
        public void onPlayListChangeError(PlayItem playItem, int i, int i1) {

        }

    };
    private Activity activity;

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_player_radio_horizontal;
    }

    @Override
    protected int getLayoutId_Tow() {
        return R.layout.fragment_player_radio_horizontal_1280_720;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initDragControllerView();
    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        this.activity = activity;
    }

    @Override
    public void initArgs() {
        super.initArgs();
        Bundle arguments = getArguments();
        if (arguments != null) {
            hasPlayerBar = arguments.getBoolean("hasPlayerBar", false);
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View root = super.onCreateView(inflater, container, savedInstanceState);
        mDragLayer = new DragLayer(getContext());
        mDragLayer.attach(root);
        Bitmap bitmap = BitmapUtils.drawableToBitmap(ResUtil.getDrawable(R.drawable.media_default_pic));
        if (bitmap != null) {
            // 关闭倒影效果，直接使用原图
            if (mProgramImage != null) {
                mProgramImage.setImageBitmap(bitmap);
            }
        }
        return mDragLayer;
    }

    @Override
    protected RadioPlayerPresenter createPresenter() {
        return new RadioPlayerPresenter(this);
    }

    @Override
    public void initView(View view) {
        mBackView =view.findViewById(R.id.backView);
        mPlayerTitleLine =view.findViewById(R.id.player_title_line);
        mProgramImage =view.findViewById(R.id.player_radio_image);
        vip_icon =view.findViewById(R.id.vip_icon);
        mPlayerTitleText =view.findViewById(R.id.player_radio_title_text);
        mTotalNumberText =view.findViewById(R.id.player_radio_title_sub_text);
        sortTypeGroup =view.findViewById(R.id.sortTypeGroup);
        sortTipTv =view.findViewById(R.id.sortTipTv);
        sortIcon =view.findViewById(R.id.sortIcon);
        sortTypeLayout =view.findViewById(R.id.sortTypeLayout);
        mPlayListView =view.findViewById(R.id.player_radio_play_list);
        mImageAnimLayout =view.findViewById(R.id.player_radio_image_anim_layout);
        mClLive =view.findViewById(R.id.cl_live);
        mTvLivetitle =view.findViewById(R.id.tv_live_title);
        tvBuy =view.findViewById(R.id.tvBuy);
        mTvLiveFlag =view.findViewById(R.id.tv_live_flag);
        mAiRadioPlusFeedbackView =view.findViewById(R.id.ai_radio_plus_feed_back_view);
        mRootLayout =view.findViewById(R.id.player_radio_root_layout);
        mRadioPlaySourceFoundView =view.findViewById(R.id.radio_play_source_found_layout);
        mTopIcon =view.findViewById(R.id.player_radio_top_icon);
        playerBarViewStub =view.findViewById(R.id.playerBarViewStub);
        mReflectionStartColor =getContext().getResources().getColor(R.color.color_cover_reflection_start);
        mReflectionEndColor =getContext().getResources().getColor(R.color.color_cover_reflection_end);


        mRootLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
        PlayerManagerHelper.getInstance().setInProgramPlayerPage(true);
        if (PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_ALBUM
                || PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_FEATURE) {
            IPlayListControl playListControl = PlayerManager.getInstance().getPlayListControl();
            boolean showSort = false;
            //和主站同步，使用是否支持订阅来判断是否支持正倒序。
            if (PlayerManager.getInstance().getPlayListInfo() != null) {
                showSort = PlayerManager.getInstance().getPlayListInfo().isEnableReverse();
            }
            //以前的逻辑，只有非即时性专辑才支持正倒序
//            if (playListControl != null) {
//                PlaylistInfo playListInfo = playListControl.getPlayListInfo();
//                if (playListInfo != null) {
//                    if (BREAK_POINT_CONTINUE_TIME.equals(playListInfo.getBreakPointContinue())) {
//                        setItemCountViewGroupVisibilityWithoutSort(View.VISIBLE);
//                        showSort = false;
//                    }
//                }
//            }
            if (showSort)
                setItemCountViewGroupVisibility(View.VISIBLE);
            setAlbumViews(showSort);
            //专辑，需要先查询排序方式后请求数据
        } else if (PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_RADIO) {
            setItemCountViewGroupVisibilityWithoutTitleView(View.GONE);
        } else if (PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM) {
            setItemCountViewGroupVisibility(View.VISIBLE);
            setAlbumViews(true);
        }

        mKRadioMultiWindowInter = ClazzImplUtil.getInter("KradioMultiWindowImpl");
        mPlayerFragmentHelper = new PlayerFragmentHelper();
        mPlayerFragmentHelper.setPresenter(mPresenter);
        mPlayListView.showPlayList();
        mPlayListView.setTopIcon(mTopIcon);
        KRadioPlayPageModifyInter inter = ClazzImplUtil.getInter("KRadioPlayPageModifyImpl");
        if (inter != null && inter.isModify()) {

        }
        initListener();
        updateProgress();
        if (hasPlayerBar && playerBarViewStub != null) {
            playerBarViewStub.inflate();
            playerBarViewStub = null;
        }
    }

    /**
     * 初始化专辑相关view
     *
     * @param showSort 是否显示排序，如果是时效性专辑，则为false，否则为true
     */
    private void setAlbumViews(boolean showSort) {
        if (!showSort) {
            return;
        }
        sortTypeLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(getContext())) {
                    return;
                }
                IPlayListControl playListControl = PlayerManager.getInstance().getPlayListControl();
                if (playListControl instanceof AlbumPlayListControl || playListControl instanceof FeaturePlayListControl) {
                    String buttonId = ButtonExposureOrClickReportEvent.BUTTON_ID_PLAYLIST_SORT_TYPE_ASC;
                    String buttonname = sortTipTv.getText().toString();
                    if (playListControl.getPlayListInfo().getSort() == PlayerConstants.SORT_ACS) {
                        //正序切换倒序
                        playListControl.getPlayListInfo().setSort(PlayerConstants.SORT_DESC);
                        sortTipTv.setText(getResources().getString(R.string.comprehensive_player_sort_negative));
                        sortTypeLayout.setContentDescription(getResources().getString(R.string.comprehensive_player_sort_negative));
                        sortIcon.setImageResource(R.drawable.comprehensive_search_sort_bottom);
                    } else {
                        //倒序切换正序
                        playListControl.getPlayListInfo().setSort(PlayerConstants.SORT_ACS);
                        sortTipTv.setText(getResources().getString(R.string.comprehensive_player_sort_positive));
                        sortTypeLayout.setContentDescription(getResources().getString(R.string.comprehensive_player_sort_positive));
                        sortIcon.setImageResource(R.drawable.comprehensive_search_sort_up);
                        buttonId = ButtonExposureOrClickReportEvent.BUTTON_ID_PLAYLIST_SORT_TYPE_DESC;
                    }
                    //正倒序需要切换后定位到当前播放项
//                    isSortTypeChanged.set(true);
//                    if (mHandler.hasMessages(LOAD_PLAY_LIST_WITH_CURRENT_PLAYITEM))
//                        mHandler.removeMessages(LOAD_PLAY_LIST_WITH_CURRENT_PLAYITEM);
//                    mHandler.sendEmptyMessage(LOAD_PLAY_LIST_WITH_CURRENT_PLAYITEM);
                    if (mPlayListView != null) {
                        mPlayListView.loadPageContaintCurrentPlayItem();
                    }

                    RadioSortTypeDaoManager.getInstance().save(PlayerManagerHelper.getInstance().getCurPlayItem(), playListControl.getPlayListInfo().getSort());
                    //数据上报
                    ButtonClickReportEvent event = new ButtonClickReportEvent(ButtonClickReportEvent.ONLINE_BUTTON_PLAY_ODER);
                    ReportHelper.getInstance().addEvent(event);

                    PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
                    if (curPlayItem != null) {
                        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_CLICK, buttonId, buttonname, ReportParameterManager.getInstance().getPage()
                                , ReportConstants.CONTROL_TYPE_SCREEN, null, curPlayItem.getAlbumId(), String.valueOf(curPlayItem.getAudioId()), curPlayItem.getAlbumId()));
                    }
                }
            }
        });
        querySortTypeFromDB();
    }

    /**
     * 查询排序方式，并设置
     */
    private void querySortTypeFromDB() {
        PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (curPlayItem != null) {
            //需要从数据库中查询该专辑的排序方式
            PlayerManagerHelper.getInstance().getAlbumOrFeatureSortType(curPlayItem.getType(), Long.parseLong(curPlayItem.getAlbumId()), new Function1<Boolean, Unit>() {
                @Override
                public Unit invoke(Boolean aBoolean) {
                    IPlayListControl playListControl = PlayerManager.getInstance().getPlayListControl();
                    if (playListControl == null) {
                        return null;
                    }
                    String buttonId = ButtonExposureOrClickReportEvent.BUTTON_ID_PLAYLIST_SORT_TYPE_ASC;
                    if (aBoolean == null) {
                        int sortType = playListControl.getPlayListInfo().getSort();
                        //如果数据库里没有该专辑的排序方式或者排序方式有误，则保存
                        RadioSortTypeDaoManager.getInstance().save(PlayerManagerHelper.getInstance().getCurPlayItem(), sortType);
                    } else {
                        if (isAdded()) {
                            if (aBoolean) {
                                playListControl.getPlayListInfo().setSort(PlayerConstants.SORT_ACS);
                                sortTipTv.setText(getResources().getString(R.string.comprehensive_player_sort_positive));
                                sortIcon.setImageResource(R.drawable.comprehensive_search_sort_up);
                            } else {
                                playListControl.getPlayListInfo().setSort(PlayerConstants.SORT_DESC);
                                sortTipTv.setText(getResources().getString(R.string.comprehensive_player_sort_negative));
                                sortIcon.setImageResource(R.drawable.comprehensive_search_sort_bottom);
                                buttonId = ButtonExposureOrClickReportEvent.BUTTON_ID_PLAYLIST_SORT_TYPE_DESC;
                            }
                        }
                    }
                    PlayItem curPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
                    if (curPlayItem != null) {
                        ReportHelper.getInstance().addEvent(new ButtonExposureOrClickReportEvent(ButtonExposureOrClickReportEvent.MODE_EXPOSURE, buttonId, sortTipTv.getText().toString(), ReportParameterManager.getInstance().getPage()
                                , ReportConstants.CONTROL_TYPE_SCREEN, null, curPlayItem.getAlbumId(), String.valueOf(curPlayItem.getAudioId()), curPlayItem.getAlbumId()));
                    }
//                    if (PlayerManagerHelper.getInstance().getCurrentPlayType() == PlayerConstants.RESOURCES_TYPE_ALBUM)
//                        mHandler.sendEmptyMessage(LOAD_PLAY_LIST_WITH_CURRENT_PLAYITEM);
                    return null;
                }
            });
        }
    }

    public void setItemCountViewGroupVisibility(int visibility) {
        ViewUtil.setViewVisibility(mPlayerTitleText, visibility);
        ViewUtil.setViewVisibility(sortTypeGroup, visibility);
    }

    public void setItemCountViewGroupVisibilityWithoutTitleView(int visibility) {
        ViewUtil.setViewVisibility(sortTypeGroup, visibility);
    }

    private void initDragControllerView() {
        mDragController = new DragController();
        mDragController.setDragListener((dragData, progress) -> {
            //不是同一个碎片就不更新进度
            if (PlayerManagerHelper.getInstance().getCurPlayItem().getAudioId() != dragData.audioId) {
                return;
            }
            PlayerManagerHelper.getInstance().seek(progress);
            ReportUtil.addPlayerUiControlEvent(PlayerUiControlReportEvent.TYPE_SELECT_PLAY_LIST, PlayerUiControlReportEvent.CONTROL_TYPE_SLIDE, String.valueOf(progress));
            /**
             * 支持无网seek,先更新ui.
             */
            PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
            playItem.setPosition(progress);
            if (playItem.getType() == PlayerConstants.RESOURCES_TYPE_LIVE_STREAM) {
                mClLive.updateProgress(progress, dragData.duration);
            } else {
                if (mPlayListView != null) {
                    mPlayListView.updateProgress(progress, playItem.getDuration());
                }
            }
        });
        initLongClickListener();
    }

    private void updateProgress() {
        PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (playItem instanceof LiveStreamPlayItem) {
            if (playItem.getStatus() == LiveStreamPlayItem.PlaybackAvailable) {
                mClLive.updateProgress(playItem.getPosition(), playItem.getDuration());
            }
        } else {
            if (mPlayListView != null) {
                mPlayListView.updateProgress(playItem.getPosition(), playItem.getDuration());
            }
        }
    }

    private void updatePlayingIcon() {
        if (mPlayListView != null) {
            mPlayListView.updatePlayingIconState();
        }
    }

    private void initLongClickListener() {
        mLongClickListener = v -> {
            PlayItem playItem;
            int progress;
            int duration;
            if (v instanceof RadioLiveItemView) {
                RadioLiveInfo radioLiveInfo = ((RadioLiveInfo) mClLive.getTag());
                if (isPlayingLiving()
                        && radioLiveInfo != null
                        && isPlayThelive(radioLiveInfo)
                        && (radioLiveInfo.getState() == LiveStreamPlayItem.PlaybackAvailable || radioLiveInfo.getState() == LiveStreamPlayItem.NoLive)) {

                    playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
                    progress = playItem.getPosition();
                    duration = playItem.getDuration();
                } else {
                    return true;
                }
            } else {
                playItem = (PlayItem) v.getTag();
                PlayItem tempPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();

                if (playItem == null || tempPlayItem == null) {
                    return true;
                }

                long audioId = playItem.getAudioId();
                if (audioId != tempPlayItem.getAudioId()) {
                    return true;
                }
                progress = tempPlayItem.getPosition();
                duration = tempPlayItem.getDuration();
            }

            DragData dragData = new DragData();
            dragData.audioId = playItem.getAudioId();
            dragData.originView = v;
            dragData.scale = (float) v.getWidth() / mDragLayer.getWidth();
            dragData.progress = progress;
            dragData.duration = duration;

            int[] location = new int[2];
            v.getLocationOnScreen(location);
            dragData.originViewInWindow = location[0];
            mDragController.startDrag(getContext(), mDragLayer, dragData);
            return true;
        };
    }


    private void initListener() {
        getContext().registerReceiver(this.receiver, new IntentFilter(HintVoiceChainIntercept.ACTION_PAY_SUCCESS));

        mBackView.setOnClickListener(v -> {
            if (AntiShake.check(v.getId())) {
                return;
            }
            if (activity instanceof BaseActivity) {
                ((BaseActivity) activity).onBackPressedSupport();
            } else {
                pop();
            }
        });

        mClLive.setOnClickListener(v -> {
            if (AntiShake.check(v.getId())) {
                return;
            }
            if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(getContext())) {
                return;
            }
            PlayItem playItem = ((RadioLiveInfo) mClLive.getTag()).toPlayItem(LivingStartListenReportEvent.POSITION_AI_RADIO);

            if (playItem.getStatus() == LiveStreamPlayItem.PlaybackGenerating) {
                return;
            }
            if (!PlayerManagerHelper.getInstance().isPlaying() ||
                    (!isPlayThelive((RadioLiveInfo) mClLive.getTag()))) {
                LiveInfoHelper.getInstance().addRadioLiveInfo(playItem.getRadioId(), (RadioLiveInfo) mClLive.getTag());
                PlayItem tempPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();
                if (tempPlayItem != null) {
                    ((LiveStreamPlayItem) playItem).getRadioInfoData().setRadioName(tempPlayItem.getRadioName());
                    PlayerLogUtil.log(getClass().getSimpleName(), "insert living stream: RadioName = " + tempPlayItem.getRadioName());
                }
                PlayerManagerHelper.getInstance().playStreamLiving(playItem);
            }

            if (playItem.isLiving()) {
                ComponentClient.obtainBuilder(LiveComponentConst.NAME)
                        .setActionName(LiveComponentConst.START_FRAGMENT)
                        .addParam("liveInfo", ((RadioLiveInfo) mClLive.getTag()).getLiveDetails())
                        .addParam("context", this)
                        .addParam("containerId", R.id.launcher_main_layout)
                        .build().callAsync();
            }
        });

        mClLive.setOnLongClickListener(mLongClickListener);
        mRadioPlayerUserObserver = new RadioPlayerUserObserver();
        ComponentUtil.addObserver(UserComponentConst.NAME, mRadioPlayerUserObserver);
    }

    private void getLive() {
        if (mPresenter != null)
            mPresenter.getLive();
    }

    private void updateLiveItemStatus() {
        if (mClLive == null || mClLive.getTag() == null) {
            return;
        }
        RadioLiveInfo radioLiveInfo = (RadioLiveInfo) mClLive.getTag();
        if (mClLive.getVisibility() == VISIBLE && radioLiveInfo != null) {
            changeLiveItem(radioLiveInfo);
            if (isPlayThelive(radioLiveInfo)) {
                showLiveCover();
            }
        }
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        mDragLayer.setupController(mDragController);
        mCurrPlayPosition = PlayerManager.getInstance().getPlayListCurrentPosition();
        mPlayItem = PlayerManagerHelper.getInstance().getCurPlayItem();

        if (mPlayItem == null) {
            //2019/1/10 场景推送,为空;添加临时监听.
            if (iPlayerStateListener == null) {
                iPlayerStateListener = new BasePlayStateListener() {
                    @Override
                    public void onPlayerPlaying(PlayItem playItem) {
                        super.onPlayerPlaying(playItem);
                        Log.i(TAG, "onPlayerPlaying " + playItem.getAudioId() + "--" + playItem.getAlbumId());
                        PlayerManager.getInstance().removePlayControlStateCallback(iPlayerStateListener);
                        //如果fragment处于销毁中,则不处理
                        Lifecycle.State currentState = RadioPlayerFragment.this.getLifecycle().getCurrentState();
                        if (currentState.isAtLeast(Lifecycle.State.CREATED)) {
                            Log.i(TAG, "#35232 onPlayerPlaying: currentState.isAtLeast(Lifecycle.State.CREATED).");
                            updateView();
                        }
                    }

                    @Override
                    public void onPlayerPaused(PlayItem playItem) {
                        super.onPlayerPaused(playItem);
                    }
                };
            }
            PlayerManager.getInstance().addPlayControlStateCallback(iPlayerStateListener);
        } else {
            updateView();
        }
        PlayerManager.getInstance().addPlayListControlStateCallback(mPlayListStateListener);
    }

    @Override
    public void onStop() {
        super.onStop();
    }


    @Override
    public void onResume() {
        super.onResume();
        Log.i(TAG, "onResume");
        watchUserStateChangeAndReplayAudioList();
    }

    private void watchUserStateChangeAndReplayAudioList() {
        UserInfoManager userInfoManager = UserInfoManager.getInstance();
        boolean loinStateChange = userInfoManager.isLoginStateChange();
        boolean vipStateChange = userInfoManager.isVipStateChange();
        Log.i(TAG, "loinStateChange:" + loinStateChange + " vipStateChange:" + vipStateChange);
        if (loinStateChange || vipStateChange) {
            replayAudioList();
        }
//        replayAudioList();
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        Log.i(TAG, "onHiddenChanged:" + hidden);
        if (!hidden) {
            watchUserStateChangeAndReplayAudioList();
        }
        if (hidden) {
            AdvertisingImager advertisingImager = AdvertisingManager.getInstance().getImager();
            if (advertisingImager != null) {
                advertisingImager.skip(null);
            }
        }
    }

    @Override
    public void onDestroyView() {
//        mPlayerControlViewNext.release();
//        mPlayerControlViewPrevious.release();
//        mPlayerControlViewPlay.release();
        if (iPlayerStateListener != null) {
            Log.i(TAG, "#35232 onDestroyView: removeIPlayerStateListener.");
            PlayerManager.getInstance().removePlayControlStateCallback(iPlayerStateListener);
        }
        if (mPlayListView != null) {
            mPlayListView.freeRes();
        }
        AudioSubscribeCacheUtil.getInstance().clear();
        ComponentUtil.removeObserver(UserComponentConst.NAME, mRadioPlayerUserObserver);
        getContext().unregisterReceiver(this.receiver);
        super.onDestroyView();
        PlayerManagerHelper.getInstance().setInProgramPlayerPage(false);
    }

    private void updateView() {
        showContent();
        addAllListener();
        if (mRadioType == PlayerConstants.RESOURCES_TYPE_ALBUM) {
            getPayInfo();
        } else if (mRadioType == PlayerConstants.RESOURCES_TYPE_VIDEO_AUDIO ||
                mRadioType == PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM) {
            vip_icon.setImageResource(R.drawable.comprehensive_icon_video);
            vip_icon.setVisibility(View.VISIBLE);
        }
    }

    private void getPayInfo() {
        if (NetworkUtil.isNetworkAvailable(getContext(), false)) {
            mPresenter.getPayInfo(Long.parseLong(PlayerManagerHelper.getInstance().getCurPlayItem().getAlbumId()));
        }
    }

    private void showContent() {
        mRadioId = mPlayerFragmentHelper.getCurrentAlbumId();
        mRadioType = PlayerManagerHelper.getInstance().getCurPlayItem().getType();
        showPlayerTitle();
        getLive();
        if (!isNowOrNextLiving()) {
            showRadioCover();
        }
        getSubscribeStatus();
        updateSubscribe(SubscribeHelper.isSubscribe);
    }

    private void updateContent() {
        ThreadUtil.runOnUI(new Runnable() {
            @Override
            public void run() {
                updateLiveItemStatus();
                if (!isNowOrNextLiving()) {
                    showRadioCover();
                }
                boolean isDiffRadio = mPlayerFragmentHelper.checkIsDiffRadio(mRadioId);
                if (isDiffRadio) {
                    if (mRadioType == PlayerConstants.RESOURCES_TYPE_ALBUM) {
                        getPayInfo();
                    }
                    if (mRadioType == PlayerConstants.RESOURCES_TYPE_RADIO || mRadioType == PlayerConstants.RESOURCES_TYPE_LIVE_STREAM) {
                        LiveStateManager.getInstance().unregisterLiveLifecycleListener(mRadioId, mLiveLifecycleListener);
                    }
                    mRadioId = mPlayerFragmentHelper.getCurrentAlbumId();
                    mRadioType = PlayerManagerHelper.getInstance().getCurPlayItem().getType();
                    getLive();
                    if (mRadioType == PlayerConstants.RESOURCES_TYPE_RADIO || mRadioType == PlayerConstants.RESOURCES_TYPE_LIVE_STREAM) {
                        LiveStateManager.getInstance().registerLiveLifecycleListener(mRadioId, mLiveLifecycleListener);
                    }
                    getSubscribeStatus();
                    showPlayerTitle();
                }
            }
        });
    }

    private void showRadioCover() {
        String coverUrl = PlayerManagerHelper.getInstance().getPlayItemPicUrl(mPlayItem);
        ViewUtil.setViewVisibility(mTvLiveFlag, GONE);
        loadCover(coverUrl);
    }

    private AlbumPayListener albumPayListener;
    private AlbumPayListener albumItemPayListener;
    private VipPayListener vipPayListener;
    private AudiosPayListener audiosPayListener;

    private void loadCover(String coverUrl) {
        // 加载为四个都是圆角的图片 可以设置圆角幅度
        ImageLoader.getInstance().getBitmapFromCache(getContext(), coverUrl, ResUtil.getDimen(R.dimen.m14), srcBitmap -> {
            if (srcBitmap.isRecycled()) {
                return;
            }
            // 关闭倒影效果，直接使用原图
            if (srcBitmap != null && mProgramImage != null) {
                mProgramImage.setImageBitmap(srcBitmap);
            }
        });
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        ImageLoader.getInstance().initDefault(ResUtil.getDrawable(R.drawable.media_default_pic),
                ResUtil.getDrawable(R.drawable.media_default_pic));
    }

    private void showPlayerTitle() {
        PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (playItem == null) {
            return;
        }
        Log.d(TAG, "showPlayerTitle " + playItem.getRadioId() + "--" + playItem.getAlbumId() + "--" + playItem.getAlbumTitle());
        String title = playItem.getRadioName();
        if (StringUtil.isEmpty(title)) {
            title = playItem.getAlbumTitle();
        }
        if (mPlayerTitleText == null) {
            return;
        }

        switch (playItem.getType()) {
            case PlayerConstants.RESOURCES_TYPE_ALBUM: {
                mPlayerTitleText.setText(com.kaolafm.kradio.lib.utils.StringUtil.getMaxString(title, 20));
                mTotalNumberText.setText(StringUtil.format(
                        getContext().getString(R.string.audio_total_num_str), ((AlbumPlayItem) playItem).getAlbumInfoData().getCountNum()));
//                mRadioPlaySourceFoundView.setTitleSourceData(playItem);
            }
            break;
            case PlayerConstants.RESOURCES_TYPE_FEATURE: {
                mPlayerTitleText.setText(com.kaolafm.kradio.lib.utils.StringUtil.getMaxString(title, 20));
                mTotalNumberText.setText(StringUtil.format(
                        getContext().getString(R.string.audio_total_num_str), ((FeaturePlayItem) playItem).getAlbumInfoData().getCountNum()));
//                mRadioPlaySourceFoundView.setTitleSourceData(playItem);
            }
            break;
            case PlayerConstants.RESOURCES_TYPE_VIDEO_ALBUM:
            case PlayerConstants.RESOURCES_TYPE_VIDEO_AUDIO:
            {
                mPlayerTitleText.setText(com.kaolafm.kradio.lib.utils.StringUtil.getMaxString(title, 20));
                mTotalNumberText.setText(StringUtil.format(
                        getContext().getString(R.string.video_total_num_str), ((VideoAlbumPlayItem) playItem).getAlbumInfoData().getCountNum()));
            }
            break;
            case PlayerConstants.RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE: {
                mPlayerTitleText.setText(getContext().getString(R.string.my_subscription_str));
            }
            break;
            default: {
                mPlayerTitleText.setText(com.kaolafm.kradio.lib.utils.StringUtil.getMaxString(title, 20));
            }
            break;
        }
    }

    // CPU优化：禁用播放器图片切换动画以降低CPU使用率
    private void managePlayAnimation() {
        // CPU优化：直接返回，不执行动画
        return;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        AdvertisingImager advertisingImager = AdvertisingManager.getInstance().getImager();
        if (advertisingImager != null) {
            advertisingImager.skip(null);
        }
        removeAllListener();
    }

    @Override
    public boolean onBackPressedSupport() {
        if (mPlayListView != null) {
            mPlayListView.removeCallback();
        }
        return super.onBackPressedSupport();

    }

    @Override
    public String getPageId() {
        return Constants.PAGE_ID_PLAYER_MUSIC_LIST;
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        mPlayListView.onConfigurationChanged();
    }

    @Override
    protected void showAccordingToScreen(int orientation) {
        super.showAccordingToScreen(orientation);
        if (orientation == Configuration.ORIENTATION_PORTRAIT) {
            changeViewToPortrait();
        } else {
            changeViewToLand();
        }
        updateProgramImageUi();
    }

//    private void watchUserStateChangeAndReplayAudioList() {
//        //todo 登陆状态改变后,vip开通后，付费后，需要重新拉取播单进行播放
//        //todo 用户登陆，vip状态监听需要放到管理类中处理，业务层监听回调
//        updateLoginState();
//        updateVipState();
//        if (loginStateChange || vipStateChange) {
//            replayAudioList();
//        }
//        initLoginState();
//        initVipState();
//    }

    private void replayAudioList() {
        Log.i(TAG, "replayAudioList");
        mRadioId = Long.parseLong(PlayerManagerHelper.getInstance().getCurPlayItem().getAlbumId()) ;
        mRadioType = PlayerManagerHelper.getInstance().getCurPlayItem().getType();
        PlayerManagerHelper.getInstance().restart(String.valueOf(mRadioId), mRadioType);
    }


    /**
     * 改变为横屏View
     */
    private void changeViewToLand() {

    }

    /**
     * 改变为竖屏view
     */
    private void changeViewToPortrait() {

    }

    private void changeFunctionTitleLineToLand(ConstraintSet set) {
        set.setVerticalBias(mPlayerTitleLine.getId(), ViewConstants.TITLE_LAND_PERCENT);
    }

    private void changeFunctionTitleLineToPortrait(ConstraintSet set) {
        if (mKRadioMultiWindowInter != null && mKRadioMultiWindowInter.getMultiStatus(this)) {
            mKRadioMultiWindowInter.doMultiPlayerFragmentTitleLine(this, set);
        } else {
            set.setVerticalBias(mPlayerTitleLine.getId(), ViewConstants.TITLE_PORT_PERCENT);
        }
    }

    /**
     * 横屏-专辑图片
     */
    private void changeFunctionProgramImageToLand(ConstraintSet set) {
        set.clear(mProgramImage.getId());
        set.connect(mProgramImage.getId(), ConstraintSet.TOP, mPlayerTitleLine.getId(), ConstraintSet.BOTTOM, getResources().getDimensionPixelOffset(R.dimen.y77));

        set.constrainWidth(mProgramImage.getId(), getResources().getDimensionPixelOffset(R.dimen.m260));
        set.constrainHeight(mProgramImage.getId(), getResources().getDimensionPixelOffset(R.dimen.m294));

        KRadioC211ViewSizeInter inter = ClazzImplUtil.getInter("KRadioC211ViewSizeImpl");
        if (inter != null && inter.isNeedReset()) {
            set.connect(mProgramImage.getId(), ConstraintSet.TOP, mPlayerTitleLine.getId(), ConstraintSet.BOTTOM,
                    getResources().getDimensionPixelOffset(R.dimen.y10));
        } else {
            set.connect(mProgramImage.getId(), ConstraintSet.TOP, mPlayerTitleLine.getId(), ConstraintSet.BOTTOM,
                    getResources().getDimensionPixelOffset(R.dimen.player_play_program_image_top_margin));
        }
        set.connect(mProgramImage.getId(), ConstraintSet.LEFT, ConstraintSet.PARENT_ID, ConstraintSet.LEFT);

    }


    /**
     * 竖屏-专辑图片
     */
    private void changeFunctionProgramImageToPortrait(ConstraintSet set) {
        set.clear(mProgramImage.getId());
        set.constrainWidth(mProgramImage.getId(), getResources().getDimensionPixelOffset(R.dimen.m240));
        set.constrainHeight(mProgramImage.getId(), getResources().getDimensionPixelOffset(R.dimen.m274));
        set.connect(mProgramImage.getId(), ConstraintSet.TOP, mPlayerTitleLine.getId(), ConstraintSet.BOTTOM, getResources().getDimensionPixelOffset(R.dimen.y59));
        set.connect(mProgramImage.getId(), ConstraintSet.LEFT, ConstraintSet.PARENT_ID, ConstraintSet.LEFT, getResources().getDimensionPixelOffset(R.dimen.x50));
        set.connect(mProgramImage.getId(), ConstraintSet.RIGHT, ConstraintSet.PARENT_ID, ConstraintSet.RIGHT, getResources().getDimensionPixelOffset(R.dimen.x50));
    }

    /**
     * 横屏-播放控制
     */
    private void changeFunctionRadioControllerBarToLand(ConstraintSet set) {
//        set.clear(mPlayerRadioControllerBar.getId());
//        set.connect(mPlayerRadioControllerBar.getId(), ConstraintSet.TOP, mProgramImage.getId(), ConstraintSet.BOTTOM, getResources().getDimensionPixelOffset(R.dimen.y2));
        if (mKRadioMultiWindowInter != null && mKRadioMultiWindowInter.getMultiStatus(this)) {
            mKRadioMultiWindowInter.doMultiPlayerFragmentRadioControllerBar(this, set);
        } else {
//            set.connect(mPlayerRadioControllerBar.getId(), ConstraintSet.LEFT, ConstraintSet.PARENT_ID, ConstraintSet.LEFT, getResources().getDimensionPixelOffset(R.dimen.x90));
//            set.connect(mPlayerRadioControllerBar.getId(), ConstraintSet.RIGHT, mPlayListView.getId(), ConstraintSet.LEFT, getResources().getDimensionPixelOffset(R.dimen.x90));
        }
//        set.constrainHeight(mPlayerRadioControllerBar.getId(), getResources().getDimensionPixelOffset(R.dimen.y96));
    }

    /**
     * 竖屏-播放控制
     */
    private void changeFunctionRadioControllerBarToPortrait(ConstraintSet set) {
//        set.clear(mPlayerRadioControllerBar.getId());
//        set.connect(mPlayerRadioControllerBar.getId(), ConstraintSet.TOP, mRadioPlayerBackground.getId(), ConstraintSet.TOP);
//        set.connect(mPlayerRadioControllerBar.getId(), ConstraintSet.BOTTOM, mRadioPlayerBackground.getId(), ConstraintSet.BOTTOM);
//        set.connect(mPlayerRadioControllerBar.getId(), ConstraintSet.LEFT, mProgramImage.getId(), ConstraintSet.RIGHT, getResources().getDimensionPixelOffset(R.dimen.x18));
//        set.connect(mPlayerRadioControllerBar.getId(), ConstraintSet.RIGHT, mRadioPlayerBackground.getId(), ConstraintSet.RIGHT);
//        set.constrainHeight(mPlayerRadioControllerBar.getId(), getResources().getDimensionPixelOffset(R.dimen.y96));
    }

    /**
     * 竖屏-直播入流
     */
    private void changeFunctionLivingToPortrait(ConstraintSet set) {
        set.clear(mClLive.getId(), ConstraintSet.TOP);
        ConstraintLayout.LayoutParams pm = (ConstraintLayout.LayoutParams) mClLive.getLayoutParams();
        pm.topMargin = getResources().getDimensionPixelOffset(R.dimen.y30);
        set.connect(mClLive.getId(), ConstraintSet.TOP, tvBuy.getId(), ConstraintSet.BOTTOM);
    }

    /**
     * 横屏-直播入流
     */
    private void changeFunctionLivingToLand(ConstraintSet set) {
        set.clear(mClLive.getId(), ConstraintSet.TOP);
        set.connect(mClLive.getId(), ConstraintSet.TOP, mPlayerTitleLine.getId(), ConstraintSet.BOTTOM, getResources().getDimensionPixelOffset(R.dimen.y52));
    }

    private void updateProgramImageUi() {
        if (mProgramImage == null) {
            return;
        }
        mProgramImage.getViewTreeObserver().addOnPreDrawListener(new ViewTreeObserver.OnPreDrawListener() {
            @Override
            public boolean onPreDraw() {
                if (mProgramImage != null && mImageAnimLayout != null) {
                    mProgramImage.getViewTreeObserver().removeOnPreDrawListener(this);
                    mImageAnimLayout.showMagicLayer(mProgramImage.getWidth());
                    mImageAnimLayout.requestLayout();
                }
                return true;
            }
        });
        updateLiveItemStatus();
    }

    @Override
    public FragmentAnimator onCreateFragmentAnimator() {
        boolean isNeedAnimation = true;// PerformanceSettingMananger.getInstance().getIsNeedAnimation();
        Log.i("BaseFragment", "isNeedAnimation:" + isNeedAnimation);
        if (isNeedAnimation) {
            return new FragmentAnimator(R.anim.anim_fade_in,
                    R.anim.anim_fade_out,
                    R.anim.anim_fade_out,
                    R.anim.anim_fade_in);
        } else {
            return new DefaultNoAnimator();
        }
    }

    /****************************************************   播放监听相关  *******************************************************************/

    private void removeAllListener() {
        if (mRadioType == PlayerConstants.RESOURCES_TYPE_RADIO || mRadioType == PlayerConstants.RESOURCES_TYPE_LIVE_STREAM) {
            LiveStateManager.getInstance().unregisterLiveLifecycleListener(mRadioId, mLiveLifecycleListener);
        }
        PlayerManager playerManager = PlayerManager.getInstance();
        playerManager.removePlayControlStateCallback(mPlayStateListener);
        playerManager.removePlayListControlStateCallback(mPlayListStateListener);
        if (mPlayerFragmentHelper != null) {
            mPlayerFragmentHelper.onDestroyPlayerFragmentHelper();
        }

        albumPayListener = null;
        albumItemPayListener = null;
        vipPayListener = null;
        audiosPayListener = null;
        if (currentPlayItemIntercept != null) {
            HintInterceptManager.getInstance().removeCurrentPlayItemIntercept(currentPlayItemIntercept);
            currentPlayItemIntercept = null;
        }

        PayManager.getInstance().clear();


//        if (albumPayListener != null) {
//            PayManager.getInstance().removeItemSingleListener(albumPayListener);
//            albumPayListener = null;
//        }
//        if (vipPayListener != null) {
//            PayManager.getInstance().removeItemSingleListener(vipPayListener);
//            vipPayListener = null;
//        }
//        if (audiosPayListener != null) {
//            PayManager.getInstance().removeItemSingleListener(audiosPayListener);
//            audiosPayListener = null;
//        }
    }

    private void addAllListener() {
        if (mRadioType == PlayerConstants.RESOURCES_TYPE_RADIO || mRadioType == PlayerConstants.RESOURCES_TYPE_LIVE_STREAM) {
            LiveStateManager.getInstance().registerLiveLifecycleListener(mRadioId, mLiveLifecycleListener);
        }
        PlayerManager playerManager = PlayerManager.getInstance();
        playerManager.addPlayControlStateCallback(mPlayStateListener);
    }

    private BasePlayStateListener mPlayStateListener = new BasePlayStateListener() {
        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            mPlayItem = playItem;
            updateContent();
            managePlayAnimation();
        }

        @Override
        public void onProgress(PlayItem playItem, long progress, long duration) {
            if (playItem != null)
                Log.i(TAG, "playItem:" + playItem.getTitle() + "onProgress:" + progress + "/" + duration);
            RadioLiveInfo radioLiveInfo = (RadioLiveInfo) mClLive.getTag();
            if (isPlayingLiving() && radioLiveInfo != null
                    && isPlayThelive(radioLiveInfo) && (radioLiveInfo.getState() == LiveStreamPlayItem.PlaybackAvailable
                    || radioLiveInfo.getState() == LiveStreamPlayItem.NoLive)) {
                if (duration > 0) {
                    mClLive.updateProgress(progress, duration);
                }
            }
        }

        @Override
        public void onPlayerFailed(PlayItem playItem, int i, int i1) {
            Log.i(TAG, "onPlayerFailed...");//do
            if (NetworkUtil.isNetworkAvailable(getContext(), true)) {
                showErrorToast(R.string.is_not_online);
            }
        }

        @Override
        public void onPlayerEnd(PlayItem playItem) {
            super.onPlayerEnd(playItem);
            //fixed 解决播放到最后一个条目时，播放结束后进度条没有更新完的问题
            updateProgress();
            updatePlayingIcon();
            Log.i(TAG, "onPlayerEnd...");
        }

        @Override
        public void onBufferingEnd(PlayItem playItem) {
            super.onBufferingEnd(playItem);
            Log.i(TAG, "onBufferingEnd...");
        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            super.onPlayerPlaying(playItem);
            updatePlayingIcon();
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            super.onPlayerPaused(playItem);
            updatePlayingIcon();
        }
    };

    /****************************************************   订阅相关  *******************************************************************/

    private void getSubscribeStatus() {
        if (NetworkUtil.isNetworkAvailable(getContext(), false)) {
            mPresenter.checkSubscribeMedia();
        }
    }


    @Override
    public void onSubscribeSuccess(int status) {
        ToastUtil.showOnly(getContext(), getString(R.string.subscribed_success_str));
    }

    @Override
    public void onSubscribeError() {
        ToastUtil.showOnly(getContext(), getString(R.string.subscribe_failed_str));
    }

    @Override
    public void onUnSubscribeSuccess(int status) {
        ToastUtil.showOnly(getContext(), getString(R.string.un_subscribed_success_str));
    }

    @Override
    public void onUnSubscribeError() {
        ToastUtil.showOnly(getContext(), getString(R.string.un_subscribe_failed_str));
    }

    @Override
    public void updateSubscribe(boolean isSubscribe) {
        Log.i(TAG, "是否已经订阅= " + isSubscribe);
        bIsSubscribed = isSubscribe;
        // 解决https://app.huoban.com/tables/2100000007530121/items/2300001219449724?userId=1881599问题
        if (mPlayerFragmentHelper != null) {
//            mPlayerFragmentHelper.updateSubscribeText(mSubscribeText, isSubscribe);
            mPlayerFragmentHelper.postSubscribeMsg(isSubscribe);
        }

        boolean isDisplay = true;
        if (PlayerManager.getInstance().getPlayListInfo() != null) {
            isDisplay = PlayerManager.getInstance().getPlayListInfo().getNoSubscribe() != 1;
        }
    }

    /****************************************************   直播相关  *******************************************************************/

    private LiveLifecycleListener mLiveLifecycleListener = new LiveLifecycleListener() {
        @Override
        public void onState(int state, RadioLiveInfo radioLiveInfo) {
            PlayerLogUtil.log(getClass().getSimpleName(), "living callback: " + state);
            if (mClLive.getVisibility() == GONE) {
                if (state == LiveStreamPlayItem.Living) {
                    showLiveItemWithAnimation();
                    fillLiveItem(radioLiveInfo);
                    switchLiveState(radioLiveInfo);
                }
                return;
            }
            if (state == LiveStreamPlayItem.Living) {
                Bundle extend = getExtend();
                String action = extend.getString("operating");
                if (TextUtils.equals(action, "yes")) {
                    fillLiveItem(radioLiveInfo);
                    switchLiveState(radioLiveInfo);
                }
            } else if (state == LiveStreamPlayItem.NoLive) {
                RadioLiveInfo currLiveInfo = (RadioLiveInfo) mClLive.getTag();
                if (currLiveInfo != null && currLiveInfo.getProgramId() == radioLiveInfo.getProgramId()) {
                    mClLive.setTag(radioLiveInfo);
                    if (!isPlayThelive(currLiveInfo)) {
                        if (radioLiveInfo.isHasPlayback()) {
                            ToastUtil.showOnly(getContext(), R.string.comprehensive_player_live_playback_finished);
                        } else {
                            ToastUtil.showOnly(getContext(), R.string.comprehensive_player_live_finished);
                        }
                        mPresenter.getLiveFromDB();
                    }
                }
            } else {
                RadioLiveInfo currLiveInfo = (RadioLiveInfo) mClLive.getTag();
                if (currLiveInfo != null && currLiveInfo.getProgramId() == radioLiveInfo.getProgramId()) {
                    switchLiveState(radioLiveInfo);
                    mClLive.setTag(radioLiveInfo);
                }
            }
        }
    };

    private void switchLiveState(RadioLiveInfo radioLiveInfo) {
        if (radioLiveInfo == null) {
            return;
        }
        switch (radioLiveInfo.getState()) {
            case LiveStreamPlayItem.Living:
                mClLive.switchLivingState();
                break;
            case LiveStreamPlayItem.PlaybackGenerating:
                mClLive.switchPlaybackGeneratingState();
                break;
            case LiveStreamPlayItem.NoLive:
            case LiveStreamPlayItem.PlaybackAvailable:
                mClLive.switchPlaybackAvailableState();
                //mClLive.showProgress();
                break;
        }
    }

    private void showLiveItemWithAnimation() {
        if (mClLive.getVisibility() == View.GONE) {
            Animation animation = AnimationUtils.loadAnimation(getContext(), R.anim.item_animation_from_right);
            animation.setAnimationListener(new Animation.AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {
                    mClLive.setVisibility(VISIBLE);
                }

                @Override
                public void onAnimationEnd(Animation animation) {
                }

                @Override
                public void onAnimationRepeat(Animation animation) {
                }
            });
            mClLive.startAnimation(animation);
            /**
             * 作动画要重新刷新一下UI, 不然在低版本手机, 动画不会正常完成.
             */
            mRootLayout.requestLayout();
        }
    }

    @Override
    public void hideLiveItemWithAnimation() {
        if (mClLive.getVisibility() == VISIBLE) {
            Animation animation = AnimationUtils.loadAnimation(getContext(), R.anim.item_animation_from_left);
            animation.setAnimationListener(new Animation.AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {
                }

                @Override
                public void onAnimationEnd(Animation animation) {
                    if (mClLive != null) {
                        mClLive.setVisibility(GONE);
                    }
                }

                @Override
                public void onAnimationRepeat(Animation animation) {

                }
            });
            mClLive.startAnimation(animation);
        }
    }

    private HintInterceptManager.OnCurrentPlayItemIntercept currentPlayItemIntercept;

    @Override
    public void showPayInfo(AlbumDetails albumDetails, boolean needBuy) {
        //设置vip或者精品角标
        setVipIcon(albumDetails);
        Log.d(TAG, "showPayInfo ,album buy status " + albumDetails.getBuyStatus() + " , album vip "
                + albumDetails.getVip() + " , album fine " + albumDetails.getFine() + " , user vip " + UserInfoManager.getInstance().getVip());
        if (albumDetails.getBuyStatus() == AlbumDetails.BUY_STATUS_NOT_PURCHASE) {
            if (albumDetails.getVip() == 1 && (!UserInfoManager.getInstance().isUserLogin()
                    || UserInfoManager.getInstance().getVip() != 1)) {
                ViewUtil.setViewVisibility(tvBuy, VISIBLE);
                tvBuy.setText(R.string.vip_btn);
                tvBuy.setSelected(true);
                tvBuy.setOnClickListener(v -> {
                    if (AntiShake.check(v.getId())) {
                        return;
                    }
                    if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(v.getContext())) {
                        return;
                    }
                    boolean userBound = UserInfoManager.getInstance().isUserBound();
                    if (!userBound) {
                        LoginManager.getInstance().setLoginInTo(LoginReportEvent.REMARKS1_PLAY_VIP_BUTTON);
                        mPlayerFragmentHelper.login2Pay(mPresenter, RadioPlayerFragment.this);
                        return;
                    }

                    buyAlbum(PayConst.PAY_TYPE_VIP);
                });
                PayManager.getInstance().addPayListener(null, vipPayListener = new VipPayListener() {

                    @Override
                    public void payResponse(PayResult payResult, PlayItem playItem, Long vipTime) {
                        Log.d(TAG, "buyAlbum VipPayListener payResponse " + payResult.toString());
                        if (payResult.getPurchaseSucess().getStatus() == 1) {
                            HintInterceptManager.getInstance().notifyPaySuccess(); //通知支付成功
                            ViewUtil.setViewVisibility(tvBuy, GONE);
                            UserInfoManager.getInstance().setVip(1);
                            PlayerManagerHelper.getInstance().refreshPlayList();
                            PayManager.getInstance().removeItemSingleListener(mPlayItem, this);
                            vipPayListener = null;
                        }
                    }
                });
                if (needBuy) {
                    buyAlbum(PayConst.PAY_TYPE_VIP);
                }
            } else if (albumDetails.getFine() == 1 && albumDetails.getBuyType() == AlbumDetails.BUY_TYPE_ALBUM) {
                ViewUtil.setViewVisibility(tvBuy, VISIBLE);
                tvBuy.setText(R.string.vip_btn2);
                tvBuy.setSelected(false);
                tvBuy.setOnClickListener(v -> {
                    if (AntiShake.check(v.getId())) {
                        return;
                    }
                    if (!NetworkUtil.isNetworkAvailableWidthDefaultToast(v.getContext())) {
                        return;
                    }
                    boolean userBound = UserInfoManager.getInstance().isUserBound();
                    if (!userBound) {
                        LoginManager.getInstance().setLoginInTo(LoginReportEvent.REMARKS1_ALUBM_PAY_BTN);
                        mPlayerFragmentHelper.login2Pay(mPresenter, RadioPlayerFragment.this);
                        return;
                    }
                    PayManager.getInstance().addPayListener(PlayerManagerHelper.getInstance().getCurPlayItem(), albumPayListener = new AlbumPayListener() {

                        @Override
                        public void payResponse(PayResult payResult, PlayItem playItem) {
                            Log.d(TAG, "buyAlbum AlbumPayListener payResponse " + payResult.toString());
                            if (payResult.getPurchaseSucess().getStatus() == 1) {
                                HintInterceptManager.getInstance().notifyPaySuccess(); //通知支付成功
                                PlayerManagerHelper.getInstance().refreshPlayList();
                                ViewUtil.setViewVisibility(tvBuy, GONE);
                                PayManager.getInstance().removeItemSingleListener(mPlayItem, this);
                                albumPayListener = null;
                            }
                        }
                    });
                    buyAlbum(PayConst.PAY_TYPE_ALBUM);
                });
                PayManager.getInstance().addPayListener(PlayerManagerHelper.getInstance().getCurPlayItem(), albumPayListener = new AlbumPayListener() {

                    @Override
                    public void payResponse(PayResult payResult, PlayItem playItem) {
                        Log.d(TAG, "buyAlbum AlbumPayListener payResponse " + payResult.toString());
                        if (payResult.getPurchaseSucess().getStatus() == 1) {
                            HintInterceptManager.getInstance().notifyPaySuccess(); //通知支付成功
                            PlayerManagerHelper.getInstance().refreshPlayList();
                            ViewUtil.setViewVisibility(tvBuy, GONE);
                            PayManager.getInstance().removeItemSingleListener(mPlayItem, this);
                            albumPayListener = null;
                        }
                    }
                });
//                HintInterceptManager.getInstance().addOnCurrentPlayItemIntercept(currentPlayItemIntercept = new HintInterceptManager.OnCurrentPlayItemIntercept() {
//                    @Override
//                    public void getHintInterceptState(PlayItem playItem, int hintType, int buyType, int buyStatus, boolean isUserLogin) {
//                        Log.i(TAG, "getHintInterceptState :" + playItem.getBuyStatus());
//                        switch (buyType) {
//                            case TYPE_ALBUM: //专辑购买
//                                PayManager.getInstance().addPayListener(playItem, albumItemPayListener = new AlbumPayListener() {
//
//                                    @Override
//                                    public void payResponse(PayResult payResult, PlayItem playItem) {
//                                        Log.d(TAG, "buyAlbum albumItemPayListener payResponse " + payResult.toString());
//                                        if (payResult.getPurchaseSucess().getStatus() == 1) {
//                                            ViewUtil.setViewVisibility(tvBuy, GONE);
//                                            PayManager.getInstance().removeItemSingleListener(mPlayItem, this);
//                                            albumItemPayListener = null;
//                                        }
//                                    }
//                                });
//                                break;
//                            default:
//                                break;
//                        }
//                    }
//                });
                if (needBuy) {
                    buyAlbum(PayConst.PAY_TYPE_ALBUM);
                }
            } else {
                ViewUtil.setViewVisibility(tvBuy, GONE);
            }
        } else {
            ViewUtil.setViewVisibility(tvBuy, GONE);
        }

    }

    private void setVipIcon(AlbumDetails albumDetails) {
        if (vip_icon == null) return;
        int icon = 0;
        //fine==1代表精品
        if (1 == albumDetails.getFine()) {
            icon = R.drawable.comprehensive_icon_supreme;
        } else if (1 == albumDetails.getVip()) {
            icon = R.drawable.comprehensive_icon_vip;
        }

        if (icon != 0) {
            vip_icon.setVisibility(View.VISIBLE);
            vip_icon.setImageResource(icon);
        } else {
            vip_icon.setImageDrawable(null);
            vip_icon.setVisibility(View.INVISIBLE);
        }
    }

    private void buyAlbum(int type) {
        PayManager.getInstance().pay(type, mPlayItem);
    }


    private void fillLiveItem(RadioLiveInfo radioLiveInfo) {
        if (radioLiveInfo == null) {
            return;
        }
        mTvLivetitle.setText(radioLiveInfo.getProgramTitle());
        mClLive.setTag(radioLiveInfo);
        changeLiveItem(radioLiveInfo);
    }

    private void changeLiveItem(RadioLiveInfo radioLiveInfo) {
        if (isPlayThelive(radioLiveInfo)) {
            if (radioLiveInfo.getState() == LiveStreamPlayItem.Living) {
                mClLive.setSelected(true);
                mClLive.hideProgress();
            } else {

                PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
                if (playItem.getDuration() > 0) {
                    mClLive.updateProgress(playItem.getPosition(), playItem.getDuration());
                }
                mClLive.setSelected(false);
            }
        } else {
            if (radioLiveInfo.getState() == LiveStreamPlayItem.NoLive) {
                if (radioLiveInfo.isHasPlayback()) {
                    ToastUtil.showOnly(getContext(), R.string.comprehensive_player_live_playback_finished);
                } else {
                    ToastUtil.showOnly(getContext(), R.string.comprehensive_player_live_finished);
                }
                mPresenter.getLiveFromDB();
                return;
            }

            mClLive.hideProgress();
            mClLive.setSelected(false);
        }
    }

    private boolean isPlayThelive(RadioLiveInfo radioLiveInfo) {
        PlayItem playItem = PlayerManagerHelper.getInstance().getCurPlayItem();
        if (playItem == null) {
            return false;
        }
        return radioLiveInfo.getProgramId() == playItem.getAudioId();
    }

    private boolean isPlayingLiving() {
        return PlayerManagerHelper.getInstance().isLivingStreamPlayer();
    }

    private boolean isNowOrNextLiving() {
        return PlayerManagerHelper.getInstance().isLivingStreamPlayer();
    }

    @Override
    public void showLiveItem(RadioLiveInfo liveInfo) {
        ViewUtil.setViewVisibility(mClLive, VISIBLE);
        fillLiveItem(liveInfo);
        switchLiveState(liveInfo);
        //   changeLivingView();
    }

    @Override
    public void showLiveCover() {
        ViewUtil.setViewVisibility(mTvLiveFlag, VISIBLE);
        RadioLiveInfo radioLiveInfo = (RadioLiveInfo) mClLive.getTag();
        if (radioLiveInfo == null) {
            return;
        }
        if (radioLiveInfo.getState() == LiveStreamPlayItem.Living) {
            mTvLiveFlag.setText(R.string.live);
            mTvLiveFlag.setBackgroundResource(R.drawable.flag_live_bg);
        } else {
            mTvLiveFlag.setText(R.string.broadcast_playBack_str);
            mTvLiveFlag.setBackgroundResource(R.drawable.flag_playback_bg);
        }
        ViewUtil.setViewVisibility(vip_icon, View.GONE);
        ViewUtil.setViewVisibility(tvBuy, View.GONE);
        loadCover(radioLiveInfo.getLivePic());

    }

    public boolean isReportFragment() {
        return true;
    }


    private class RadioPlayerUserObserver implements DynamicComponent, MainThreadable {

        @Override
        public String getName() {
            return "RadioPlayer-UserObserver";
        }

        @Override
        public boolean onCall(RealCaller caller) {
            String actionName = caller.actionName();
            switch (actionName) {
                case UserStateObserverProcessorConst.USER_LOGIN:
                    getPayInfo();
                    replayAudioList();
                    break;
                case UserStateObserverProcessorConst.USER_LOGOUT:
                    getPayInfo();
                    replayAudioList();
                    break;
                default:
                    break;
            }
            return false;
        }

        @Override
        public Boolean shouldActionRunOnMainThread(String actionName, ComponentClient caller) {
            return true;
        }
    }


    @Override
    protected void changeViewLayoutForStatusBar(View view) {

    }

    @Override
    protected void addFragmentRootViewPadding(View view) {

    }

    @Override
    protected boolean autoSetBackViewMarginLeft() {
        return false;
    }
}
