package com.kaolafm.kradio.flavor.impl;

import android.app.Activity;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppManager;
import com.kaolafm.kradio.lib.base.flavor.KRadioStopAudioInter;
import com.kaolafm.kradio.lib.base.lifecycle.ActivityLifecycle;

/**
 * 主要解决多屏切换时播放暂停的问题
 */
public class KRadioStopAudioImpl implements KRadioStopAudioInter {

    private static final String TAG = "KRadioStopAudioImpl";

    // 用于记录最近的HubActivity创建时间，帮助判断多屏切换
    private static volatile long lastHubActivityCreateTime = 0;
    private static final long MULTI_SCREEN_SWITCH_WINDOW = 10000; // 10秒窗口期

    @Override
    public boolean isStopAudio(Object... objs) {
        if (objs == null || objs.length == 0) {
            Log.i(TAG, "isStopAudio: no parameters, default to stop audio");
            return true;
        }

        Object firstParam = objs[0];
        if (!(firstParam instanceof Activity)) {
            Log.i(TAG, "isStopAudio: first parameter is not Activity, default to stop audio");
            return true;
        }

        Activity currentActivity = (Activity) firstParam;
        String activityName = currentActivity.getClass().getSimpleName();

        // 检测是否为多屏切换场景
        boolean isMultiScreenSwitch = isMultiScreenSwitchScenario(currentActivity);

        Log.i(TAG, "isStopAudio: activity=" + activityName
                + ", isMultiScreenSwitch=" + isMultiScreenSwitch
                + ", lastHubCreateTime=" + lastHubActivityCreateTime
                + ", currentTime=" + System.currentTimeMillis());

        if (isMultiScreenSwitch) {
            Log.i(TAG, "isStopAudio: detected multi-screen switch, keep audio playing");
            return false; // 多屏切换时不停止音频
        }

        Log.i(TAG, "isStopAudio: normal app exit, stop audio");
        return true; // 正常退出时停止音频
    }

    /**
     * 供HubActivity调用，记录创建时间
     * 这个方法应该在HubActivity的onCreate中调用
     */
    public static void notifyHubActivityCreated() {
        lastHubActivityCreateTime = System.currentTimeMillis();
        Log.i("KRadioStopAudioImpl", "notifyHubActivityCreated: " + lastHubActivityCreateTime);
    }
    
    /**
     * 检测是否为多屏切换场景
     * 优化后的判断逻辑，结合多种检测方法提高准确性
     *
     * @param currentActivity 当前正在销毁的Activity
     * @return true表示是多屏切换，false表示正常退出
     */
    private boolean isMultiScreenSwitchScenario(Activity currentActivity) {
        try {
            String activityName = currentActivity.getClass().getSimpleName();
            long currentTime = System.currentTimeMillis();

            // 方法1：检查是否在HubActivity创建的时间窗口内
            // 这是最准确的多屏切换检测方法
            if (lastHubActivityCreateTime > 0) {
                long timeSinceHubCreate = currentTime - lastHubActivityCreateTime;
                if (timeSinceHubCreate <= MULTI_SCREEN_SWITCH_WINDOW) {
                    Log.i(TAG, "isMultiScreenSwitchScenario: within HubActivity create window ("
                            + timeSinceHubCreate + "ms), confirmed multi-screen switch");
                    return true;
                }
            }

            // 方法2：检查应用是否仍在前台
            // 如果应用仍在前台，说明有其他Activity在运行，很可能是多屏切换
            boolean isAppStillForeground = isAppStillInForeground();
            if (isAppStillForeground) {
                Log.i(TAG, "isMultiScreenSwitchScenario: app still in foreground, likely multi-screen switch");
                return true;
            }

            // 方法3：检查是否有其他Activity实例存在
            // 在多屏环境下，通常会有多个Activity实例同时存在
            boolean hasOtherActivities = hasOtherActivityInstances(currentActivity);
            if (hasOtherActivities) {
                Log.i(TAG, "isMultiScreenSwitchScenario: other activities exist, likely multi-screen switch");
                return true;
            }

            // 方法4：特殊情况 - 如果是LauncherActivity且不是任务根，可能是多屏切换
            if ("LauncherActivity".equals(activityName) && !currentActivity.isTaskRoot()) {
                Log.i(TAG, "isMultiScreenSwitchScenario: LauncherActivity not task root, might be multi-screen switch");
                // 这个判断需要结合其他条件，单独使用不够准确
            }

            Log.i(TAG, "isMultiScreenSwitchScenario: no clear indicators of multi-screen switch");
            return false;

        } catch (Exception e) {
            Log.e(TAG, "isMultiScreenSwitchScenario: error during detection", e);
            // 出现异常时，为了安全起见，默认不停止音频
            return true;
        }
    }
    
    /**
     * 检查应用是否仍在前台
     * 通过ActivityLifecycle的前台Activity集合来判断
     */
    private boolean isAppStillInForeground() {
        try {
            // 使用ActivityLifecycle的isAppForeground方法
            // 这个方法基于前台Activity的数量来判断
            return com.kaolafm.kradio.lib.base.AppDelegate.getInstance().isAppForeground();
        } catch (Exception e) {
            Log.e(TAG, "isAppStillInForeground: error", e);
            return false;
        }
    }
    
    /**
     * 检查是否有其他Activity实例存在
     * 在多屏环境下，通常会有多个Activity实例
     */
    private boolean hasOtherActivityInstances(Activity currentActivity) {
        try {
            AppManager appManager = AppManager.getInstance();
            if (appManager == null) {
                return false;
            }
            
            // 获取当前管理的Activity数量
            // 如果除了当前Activity外还有其他Activity，说明可能是多屏切换
            Activity currentManagedActivity = appManager.getCurrentActivity();
            
            // 简单的判断：如果当前管理的Activity不是正在销毁的Activity，
            // 说明有其他Activity在运行
            if (currentManagedActivity != null && currentManagedActivity != currentActivity) {
                Log.i(TAG, "hasOtherActivityInstances: found other activity: " 
                        + currentManagedActivity.getClass().getSimpleName());
                return true;
            }
            
            return false;
        } catch (Exception e) {
            Log.e(TAG, "hasOtherActivityInstances: error", e);
            return false;
        }
    }
}
