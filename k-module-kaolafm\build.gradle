import com.kaolafm.gradle.plugin.Util

apply plugin: 'micro-module'
apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'product-flavor'
apply plugin: 'org.greenrobot.greendao'
apply from: '../aop.gradle'
apply from: '../test.gradle'

def and = rootProject.ext.android
def dependent = rootProject.ext.dependencies
def jpush = rootProject.ext.jpush
ext.alwaysLib = true

// 获取当前的git的commit_id
def getGitRevision() {
    return "git rev-parse --short HEAD".execute().text.trim()
}
// 给gradle.properties中的GITEST_COMMIT_ID赋值
GIT_COMMIT_ID = getGitRevision()

android {
    compileSdkVersion and.compileSdkVersion
    buildToolsVersion and.buildToolsVersion


    defaultConfig {
        renderscriptTargetApi 27 //must match target sdk and build tools
        renderscriptSupportModeEnabled true
        minSdkVersion and.minSdkVersion
        targetSdkVersion and.targetSdkVersion
        versionCode 1
        versionName "1.0"
        multiDexEnabled true
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [
                        moduleName         : project.getName(),
                        // 阿里路由生成的文档路径 :
                        // build/generated/source/apt/(debug or release)/com/alibaba/android/arouter/docs/arouter-map-of-${moduleName}.json
                        AROUTER_MODULE_NAME: project.getName(),
                        AROUTER_GENERATE_DOC: "enable"
                ]
            }
        }
        manifestPlaceholders = [
                JPUSH_PKGNAME: jpush["jpush-pkgname"],
                JPUSH_APPKEY : jpush["jpush-appkey"], //JPush上注册的包名对应的appkey�?*换成你的*�?
                JPUSH_CHANNEL: jpush["jpush-channel"], //暂时填写默认值即�?.
        ]

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        ndk {
            abiFilters "armeabi"
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            buildConfigField "String", "BUILD_TIME", "\"${getBuildTime()}\""
            buildConfigField "String", "GIT_COMMIT_ID", "\"${GIT_COMMIT_ID}\""
        }
        debug {
            buildConfigField "String", "BUILD_TIME", "\"${getBuildTime()}\""
            buildConfigField "String", "GIT_COMMIT_ID", "\"${GIT_COMMIT_ID}\""
        }
        prelease.initWith(android.buildTypes.release)
    }
    compileOptions {
        sourceCompatibility and.javaSourceVersion
        targetCompatibility and.javaTargetVersion
    }
//    sourceSets {
//        main {
//            jniLibs.srcDirs = ['main/libs']
//        }
//    }
    dexOptions {
        javaMaxHeapSize "4g"
    }
    buildFeatures{
        viewBinding true
    }
}

def getBuildTime() {
    return new Date().format("yyMMddHHmm")
}



microModule {
    def pattern = Util.getBuildPattern(rootProject)
    if (pattern.contains("comprehensive")) {
        include ':homeComprehensive'
        include ':commonComprehensive'
        include ':playerComprehensive'
        include ':userComprehensive'
        include ':historyComprehensive'
        include ':liveComprehensive'
        include ':subscribeComprehensive'
        include ':settingComprehensive'
        include ':purchaseComprehensive'
        include ':activityComprehensive'
        include ':skinComprehensive'
        include ':coin'
        include ':adComprehensive'

        include ':messageComprehensive'
        include ':brandPageComprehensive'

    }
    if (pattern.contains("online")) {
        include ':onlineHome'
        include ':onlineMine'
        include ':onlineCategories'
        include ':onlineCommon'
        include ':onlinePlayer'
        include ':onlinePurchase'
        include ':onlineSearch'
        include ':onlineSubscriptions'
        include ':onlineHistory'
        include ':onlineSkin'
        include ':onlineActivity'
        include ':onlineSetting'
        include ':onlineUser'
        include ':onlineAd'
        include ':onlineMessage'
    }

    include ':clientControlerForKradio'
    include ':common'
    include ':home'
    include ':user'
    include ':notify'
    include ':scene'
    include ':player'
    include ':history'
    include ':search'
    include ':live'
    include ':subscribe'
    include ':purchase'
    include ':setting'
    include ':basedb'
    include ':message'
    include ':mainTab'
    include ':brandPage'
}

dependencies {
    annotationProcessor project(":annotation")
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    api project(':k-component-ui')

    annotationProcessor dependent["persistence-room-compiler"]
    annotationProcessor dependent["arouter-compiler"]
    annotationProcessor dependent["glide-compiler"]
    implementation dependent['SmartRefreshHorizontal']
}

