apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'product-flavor'
apply plugin: 'auto-register'
apply from:'../aop.gradle'

def and = rootProject.ext.android
//def jpush = rootProject.ext.jpush

android {
    compileSdkVersion and.compileSdkVersion
    buildToolsVersion and.buildToolsVersion

    defaultConfig {
        applicationId "com.edog.car"
        minSdkVersion and.minSdkVersion
        targetSdkVersion and.targetSdkVersion
        versionCode and.versionCode
        versionName and.versionName
        multiDexEnabled true
        vectorDrawables.useSupportLibrary = true
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [moduleName: project.getName()]
            }
        }
        ndk {
            abiFilter("arm64-v8a")
        }

        manifestPlaceholders = [
                AMAP_API_KEY :  amap["amap-appkey"],
                MAIN_ACTIVITY_LAUNCH_MODE: "standard",
                HAS_EXPORTED: "true"
        ]
    }
    signingConfigs {
        release {
            storeFile file('edog-car-clinent-key.keystore')//签名文件路径，
            //keystore的路径通常使用项目根目录的相对路径，但也可以是使用绝对路径，尽管这不推荐
            storePassword 'lkfmedogcar' //密码
            keyAlias 'edog-car'
            keyPassword 'lkfmedogcar'  //密码
            v1SigningEnabled true
            v2SigningEnabled true
        }
        debug {
            storeFile file('edog-car-clinent-key.keystore')//签名文件路径，
            //keystore的路径通常使用项目根目录的相对路径，但也可以是使用绝对路径，尽管这不推荐
            storePassword 'lkfmedogcar' //密码
            keyAlias 'edog-car'
            keyPassword 'lkfmedogcar'  //密码
            v1SigningEnabled true
            v2SigningEnabled true
        }
        prelease.initWith(android.signingConfigs.release)
    }

    buildTypes {
        debug {
            minifyEnabled false
            shrinkResources false
            debuggable true
            signingConfig signingConfigs.debug
        }
        release {
            minifyEnabled true
            shrinkResources false
            zipAlignEnabled true
            debuggable false
            jniDebuggable false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        prelease {
            minifyEnabled true
            shrinkResources false
            zipAlignEnabled true
            debuggable true
            jniDebuggable true
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    compileOptions {
        sourceCompatibility and.javaSourceVersion
        targetCompatibility and.javaTargetVersion
    }
    dexOptions {
        javaMaxHeapSize "4g"
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    packagingOptions {
        pickFirst 'META-INF/NOTICE.txt'
        pickFirst 'META-INF/LICENSE.txt'
        pickFirst 'META-INF/DEPENDENCIES'
        pickFirst 'META-INF/NOTICE'
        pickFirst 'META-INF/LICENSE'

        pickFirst 'lib/arm64-v8a/liblocSDK8b.so'

        pickFirst 'lib/x86/librsjni_androidx.so'
        pickFirst 'lib/x86_64/librsjni_androidx.so'
        pickFirst 'lib/arm64-v8a/librsjni_androidx.so'
        pickFirst 'lib/armeabi-v7a/librsjni_androidx.so'

        pickFirst 'lib/x86/librsjni.so'
        pickFirst 'lib/x86_64/librsjni.so'
        pickFirst 'lib/arm64-v8a/librsjni.so'
        pickFirst 'lib/armeabi-v7a/librsjni.so'

        pickFirst 'lib/x86/libRSSupport.so'
        pickFirst 'lib/x86_64/libRSSupport.so'
        pickFirst 'lib/arm64-v8a/libRSSupport.so'
        pickFirst 'lib/armeabi-v7a/libRSSupport.so'

    }
}
dependencies {
    def dependent = rootProject.ext.dependencies
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation project(':k-module-kaolafm')
    implementation project(':k-base-flavor')

    debugImplementation dependent["canary-debug"]
}
/**
 * 此逻辑目的为APP的VersionCode末尾追加各渠道配置的minorVersion内容
 */
android.applicationVariants.all { variant ->
    variant.outputs.all { output ->
        String minorVersion = variant.productFlavors[0].getProperties().get("versionNameSuffix", null)
        if (minorVersion != null) {
            minorVersion = minorVersion.substring(1, minorVersion.length())
            output.versionCodeOverride = Integer.valueOf(android.defaultConfig.versionCode + minorVersion)
        }
        //修改apk的名称
        String flavorName = variant.flavorName
        if (flavorName.endsWith("_kradio")) {
            flavorName = flavorName.replace("_kradio", "")
        }
        outputFileName = "K-radio_${flavorName}_${variant.buildType.name}_${output.versionCode}.apk"
    }
}
